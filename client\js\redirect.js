// Script per gestire il reindirizzamento degli utenti loggati alla versione home-logged.html

document.addEventListener('DOMContentLoaded', function() {
    // TEMPORANEAMENTE DISABILITATO per debug del refresh continuo
    console.log('[REDIRECT] Sistema di redirect temporaneamente disabilitato per debug');
    // checkUserAuthStatus();
});

/**
 * Controlla se l'utente è loggato e reindirizza alla pagina corretta
 */
function checkUserAuthStatus() {
    // Definisci le pagine
    const GUEST_PAGE = '/';
    const LOGGED_PAGE = '/home';
    
    // Ottieni il nome del file corrente
    const currentPath = window.location.pathname;
    const isHomePage = currentPath === '/' || currentPath.endsWith('index.html');
    const isLoggedHomePage = currentPath === '/home' || currentPath.endsWith('home-logged.html');
    const isNoLoggedPage = currentPath.endsWith('no-logged.html');
    
    // Controlla se authUtils è disponibile
    if (window.authUtils && window.authUtils.isLoggedIn) {
        const isLogged = window.authUtils.isLoggedIn();

        // PROTEZIONE: Se abbiamo dati di gioco in sessionStorage, evita qualsiasi redirect
        const hasGameData = sessionStorage.getItem('gameData') !== null;
        if (hasGameData) {
            console.log('[REDIRECT] Dati di gioco rilevati in sessionStorage, evito qualsiasi redirect');
            return;
        }
        
        // Controllo se ci sono parametri URL speciali che potrebbero indicare che stiamo partecipando a un gioco
        const urlParams = new URLSearchParams(window.location.search);
        const hasGameAction = urlParams.has('action') ||
                              urlParams.has('resumeMultiplayer') ||
                              urlParams.has('gameId');

        // Controllo se siamo in modalità gioco (la schermata di gioco è visibile)
        const inGameMode = document.getElementById('game-container') &&
                           document.getElementById('game-container').style.display !== 'none';

        // Controllo se abbiamo dati di gioco in sessionStorage (partita multiplayer in corso)
        const hasGameData = sessionStorage.getItem('gameData') !== null;
        
        // Debug
        console.log('[REDIRECT] Stato di login:', isLogged);
        console.log('[REDIRECT] Pagina corrente:', currentPath);
        console.log('[REDIRECT] Azioni di gioco:', hasGameAction);
        console.log('[REDIRECT] Modalità gioco:', inGameMode);
        
        // Gestione delle diverse pagine in base allo stato di login
        
        // Caso 1: Utente nella home page principale
        if (isHomePage) {
            // Se l'utente è loggato, SEMPRE reindirizza a home-logged
            if (isLogged) {
                console.log('[REDIRECT] Utente loggato in pagina principale, reindirizzo a /home');
                window.location.href = LOGGED_PAGE;
                return;
            } else {
                // Se l'utente non è loggato, assicurati che la vista ospite sia visibile
                const guestView = document.getElementById('guest-title-view');
                const loggedView = document.getElementById('logged-user-title-view');
                if (guestView && loggedView) {
                    guestView.style.display = 'block';
                    loggedView.style.display = 'none';
                }
            }
        }
        
        // Caso 2: Utente nella pagina no-logged  
        if (isNoLoggedPage) {
            // Se l'utente è loggato, reindirizza alla home dei loggati
            if (isLogged) {
                console.log('[REDIRECT] Utente loggato in no-logged.html, reindirizzo a /home');
                window.location.href = LOGGED_PAGE;
                return;
            }
        }
        
        // Caso 3: Utente nella pagina per loggati
        if (isLoggedHomePage) {
            // Se l'utente non è loggato, reindirizza alla home principale
            if (!isLogged) {
                console.log('[REDIRECT] Utente non loggato in pagina utenti, reindirizzo a pagina principale');
                window.location.href = GUEST_PAGE;
                return;
            }
            
            // Se l'utente è in modalità gioco o ha dati di gioco pendenti, non forzare il reindirizzamento
            if (hasGameAction || inGameMode || hasGameData) {
                console.log('[REDIRECT] Utente in pagina utenti con azione di gioco attiva o dati di gioco pendenti, nessun reindirizzamento');
                return;
            }
            
            // Assicurati che le modifiche specifiche per utenti loggati siano applicate
            applyLoggedUserCustomizations();
        }
    } else {
        // Se authUtils non è ancora disponibile, riprova tra 200ms
        console.log('[REDIRECT] authUtils non ancora disponibile, riprovo tra 200ms');
        setTimeout(checkUserAuthStatus, 200);
    }
}

/**
 * Applica personalizzazioni specifiche per utenti loggati
 */
function applyLoggedUserCustomizations() {
    // Aggiungi la classe logged-user al body se non è già presente
    document.body.classList.add('logged-user');
    
    // Trova l'elemento hero-section e aggiungi la classe logged-hero se non è già presente
    const heroSection = document.querySelector('.hero-section');
    if (heroSection) {
        heroSection.classList.remove('guest-hero');
        heroSection.classList.add('logged-hero');
        
        // Nascondi la vista per ospiti
        const guestTitleView = document.getElementById('guest-title-view');
        if (guestTitleView) {
            guestTitleView.style.display = 'none';
        }
        
        // Mostra la vista per utenti loggati
        const loggedUserTitleView = document.getElementById('logged-user-title-view');
        if (loggedUserTitleView) {
            loggedUserTitleView.style.display = 'block';
        }
        
        // Nascondi la board-preview se presente
        const boardPreview = document.querySelector('.board-preview');
        if (boardPreview && heroSection.classList.contains('logged-hero')) {
            boardPreview.style.display = 'none';
        }
        
        console.log('[REDIRECT] Personalizzazioni per utenti loggati applicate alla hero section');
    }
    
    // Mostra le feature boxes per utenti loggati
    const featureBoxes = document.querySelector('.feature-boxes');
    if (featureBoxes) {
        featureBoxes.style.display = 'flex';
        console.log('[REDIRECT] Feature boxes mostrate per utenti loggati');
    }
}