/**
 * smooth-loading.js
 * 
 * Gestisce l'apparizione fluida della players-column e prevenzione glitch
 */

(function() {
    'use strict';
    
    let loadingAnimationComplete = false;
    let playersColumnVisible = false;
    
    // Funzione per attivare le animazioni
    function triggerLoadingAnimations() {
        if (loadingAnimationComplete) return;
        
        console.log('[SMOOTH LOADING] Avvio animazioni di caricamento');
        
        // Rimuovi la classe di caricamento dal body
        document.body.classList.remove('game-loading');
        
        // Attiva animazioni con timing scaglionato
        const playersColumn = document.getElementById('players-column');
        const player1Area = document.getElementById('player1-area');
        const player2Area = document.getElementById('player2-area');
        const playerInfoBoxes = document.querySelectorAll('.player-info-box');
        const playerNames = document.querySelectorAll('.player-name');
        
        if (playersColumn) {
            playersColumn.classList.add('loaded');
            playersColumnVisible = true;
        }
        
        // Player areas con delay
        setTimeout(() => {
            if (player1Area) player1Area.classList.add('loaded');
        }, 100);
        
        setTimeout(() => {
            if (player2Area) player2Area.classList.add('loaded');
        }, 200);
        
        // Player info boxes
        setTimeout(() => {
            playerInfoBoxes.forEach(box => {
                box.classList.add('loaded');
            });
        }, 400);
        
        // Player names
        setTimeout(() => {
            playerNames.forEach(name => {
                name.classList.add('loaded');
            });
        }, 600);
        
        loadingAnimationComplete = true;
        console.log('[SMOOTH LOADING] Animazioni di caricamento completate');
    }
    
    // Funzione per gestire la transizione durante il matchmaking
    function handleMatchmakingTransition() {
        console.log('[SMOOTH LOADING] Gestendo transizione matchmaking');
        
        // Aggiungi classe per gestire il matchmaking
        document.body.classList.add('matchmaking-active');
        
        // Forza la visibilità immediata durante il matchmaking
        const playersColumn = document.getElementById('players-column');
        if (playersColumn) {
            playersColumn.style.opacity = '1';
            playersColumn.style.transform = 'translateX(0)';
        }
        
        // Trigger delle animazioni dopo un breve delay
        setTimeout(() => {
            triggerLoadingAnimations();
        }, 100);
    }
    
    // Funzione per gestire la fine del matchmaking
    function handleMatchmakingEnd() {
        console.log('[SMOOTH LOADING] Fine matchmaking, rimuovo classe');
        document.body.classList.remove('matchmaking-active');
    }
    
    // Inizializza il sistema
    function initialize() {
        console.log('[SMOOTH LOADING] Inizializzazione sistema loading fluido');
        
        // Aggiungi classe di loading iniziale
        document.body.classList.add('game-loading');
        
        // Ascolta eventi di caricamento della pagina
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', () => {
                setTimeout(triggerLoadingAnimations, 300);
            });
        } else {
            setTimeout(triggerLoadingAnimations, 300);
        }
        
        // Ascolta eventi di visibilità della pagina
        document.addEventListener('visibilitychange', () => {
            if (document.visibilityState === 'visible' && !loadingAnimationComplete) {
                setTimeout(triggerLoadingAnimations, 100);
            }
        });
        
        // Ascolta eventi del game container
        const gameContainer = document.getElementById('game-container');
        if (gameContainer) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' && mutation.attributeName === 'style') {
                        const display = gameContainer.style.display;
                        if (display === 'grid' || display === 'block' || display === '') {
                            if (!loadingAnimationComplete) {
                                setTimeout(triggerLoadingAnimations, 100);
                            }
                        }
                    }
                });
            });

            observer.observe(gameContainer, {
                attributes: true,
                attributeFilter: ['style']
            });
        }
        
        // Intercetta eventi di matchmaking
        document.addEventListener('matchmakingStarted', handleMatchmakingTransition);
        document.addEventListener('matchmakingEnded', handleMatchmakingEnd);
        document.addEventListener('gameContainerShown', () => {
            if (!loadingAnimationComplete) {
                setTimeout(triggerLoadingAnimations, 50);
            }
        });
    }
    
    // Funzione per forzare l'animazione (utile per debug)
    function forceLoadingAnimation() {
        loadingAnimationComplete = false;
        triggerLoadingAnimations();
    }
    
    // Funzione per resettare le animazioni
    function resetAnimations() {
        console.log('[SMOOTH LOADING] Reset animazioni');
        
        loadingAnimationComplete = false;
        playersColumnVisible = false;
        
        document.body.classList.add('game-loading');
        document.body.classList.remove('matchmaking-active');
        
        // Rimuovi classi loaded
        const elementsToReset = [
            '#players-column',
            '#player1-area', 
            '#player2-area',
            '.player-info-box',
            '.player-name'
        ];
        
        elementsToReset.forEach(selector => {
            const elements = document.querySelectorAll(selector);
            elements.forEach(el => el.classList.remove('loaded'));
        });
    }
    
    // Gestisci l'intercettazione degli eventi socket per matchmaking
    const originalSocketOn = window.socket?.on;
    if (originalSocketOn) {
        const wrappedSocketOn = function(event, callback) {
            if (event === 'matchFound') {
                const wrappedCallback = function(...args) {
                    handleMatchmakingTransition();
                    return callback.apply(this, args);
                };
                return originalSocketOn.call(this, event, wrappedCallback);
            }
            return originalSocketOn.call(this, event, callback);
        };
        
        if (window.socket) {
            window.socket.on = wrappedSocketOn;
        }
    }
    
    // Esponi API pubblica
    window.smoothLoading = {
        trigger: triggerLoadingAnimations,
        force: forceLoadingAnimation,
        reset: resetAnimations,
        isComplete: () => loadingAnimationComplete,
        handleMatchmaking: handleMatchmakingTransition,
        endMatchmaking: handleMatchmakingEnd
    };
    
    // Inizializza
    initialize();
    
    console.log('[SMOOTH LOADING] Sistema di loading fluido caricato');
})();